<template>
  <view class="etc-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">ETC办理</view>
      <view class="nav-subtitle">选择适合您的ETC卡类型</view>
    </view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 加载状态 -->
      <view v-if="loading && boxData.length === 0" class="loading-container">
        <view class="loading-text">正在加载ETC数据...</view>
      </view>

      <!-- 空数据状态 -->
      <view v-else-if="!loading && boxData.length === 0" class="empty-container">
        <view class="empty-text">暂无ETC数据</view>
      </view>

      <!-- ETC数据列表 -->
      <view v-for="(item, index) in boxData" :key="item.id || `etc-${index}`" class="etc-section">
        <!-- 区域标题 -->
        <view class="section-title">
          <view class="title-indicator"></view>
          <text class="title-text">{{ item.title }}</text>
          <view class="title-badge" v-if="item.type === '1'">
            <text>可办理</text>
          </view>
        </view>

        <!-- 二维码图片 -->
        <view v-if="item.type === '0'" class="qr-container">
          <image :src="item.img" mode="widthFix" class="qr-image" show-menu-by-longpress="true"></image>
          <view class="qr-tip">
            <text>长按保存二维码</text>
          </view>
        </view>

        <!-- 可办理卡片 -->
        <view v-else class="etc-card" @click="toUrl(item)">
          <view class="card-content">
            <view class="card-image-container">
              <image :src="item.img" mode="aspectFit" class="card-image"></image>
            </view>
            <view class="card-info">
              <view class="card-name">{{ item.title }}</view>
              <view class="card-desc">一卡畅通全国高速</view>
              <view class="card-features">
                <view class="feature-item">
                  <text>免费办理</text>
                </view>
                <view class="feature-item">
                  <text>全国通用</text>
                </view>
              </view>
            </view>
            <view class="card-action">
              <text>办理</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getActiveEtcQrcodeList } from "@/api/etc.js";

export default {
  data() {
    return {
      boxData: [],
      carId: "",
      loading: false,
    };
  },

  onPullDownRefresh() {
    this.loadEtcData().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    // 加载ETC数据
    async loadEtcData() {
      console.log("loadEtcData方法被调用");
      try {
        this.loading = true;
        uni.showLoading({
          title: "加载中...",
        });

        console.log("开始调用getActiveEtcQrcodeList API");
        const res = await getActiveEtcQrcodeList();
        console.log("小程序端获取ETC数据响应:", res);

        // 小程序端的请求工具返回的数据结构：res.data 是后端返回的数据
        let responseData;
        if (res && res.data) {
          // 如果res.data是字符串，需要解析
          if (typeof res.data === 'string') {
            try {
              responseData = JSON.parse(res.data);
            } catch (e) {
              console.error("解析响应数据失败:", e);
              responseData = null;
            }
          } else {
            responseData = res.data;
          }
        }

        if (responseData && responseData.code === 200) {
          const etcList = responseData.data?.records || responseData.data || [];
          console.log("解析后的ETC列表:", etcList);

          // 检查每个项目的status字段
          etcList.forEach((item, index) => {
            console.log(`ETC项目${index + 1} - ID: ${item.id}, 名称: ${item.etcName}, 状态: ${item.status}`);
          });

          // 只处理启用状态(status=1)的数据，进行二次过滤确保安全
          const activeEtcList = etcList.filter(item => item.status === 1);
          console.log("过滤后的启用ETC列表:", activeEtcList);

          // 将后端数据转换为前端需要的格式
          this.boxData = activeEtcList.map(item => ({
            id: item.id,
            title: item.etcName,
            img: item.etcUrl,
            type: "0", // 默认都是二维码类型
            createTime: item.createTime,
            updateTime: item.updateTime
          }));

          console.log("转换后的boxData:", this.boxData);

        } else {
          console.error("获取ETC数据失败:", responseData);
          uni.showToast({
            title: responseData?.message || "获取数据失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("加载ETC数据出错:", error);
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      } finally {
        this.loading = false;
        uni.hideLoading();
      }
    },



    // 跳转到办理页面
    toUrl(item) {
      if (item.title === "世纪恒通卡") {
        let toPath =
          "pages/empty_hair/new_module/select_etc_handle_type/select_etc_handle_type?isNewTrucks=0&shopId=1303036541465796608&vehPlates=" +
          this.carId +
          '&extend={"merchantCode":"PPVMZIYXEXGDRCZOG"}';

        uni.showLoading({
          title: "正在跳转...",
        });

        uni.navigateToMiniProgram({
          appId: "wxddb3eb32425e4a96",
          path: toPath,
          extraData: {
            type: "out",
          },
          envVersion: "release",
          success: () => {
            uni.hideLoading();
            uni.showToast({
              title: "跳转成功",
              icon: "success",
            });
          },
          fail: (err) => {
            uni.hideLoading();
            console.error("跳转失败:", err);
            uni.showToast({
              title: "跳转失败，请重试",
              icon: "none",
            });
          },
        });
      }
    },
  },
  onLoad: function (option) {
    this.carId = option.carId;
    console.log("ETC页面onLoad，carId:", this.carId);
    console.log("开始加载ETC数据");
    this.loadEtcData();
  },
};
</script>

<style lang="scss" scoped>
/* 页面容器 */
.etc-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 内容区域 */
.content-wrapper {
  padding: 20px;
}

.etc-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 40px;
  }
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .title-indicator {
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
    border-radius: 2px;
    margin-right: 12px;
  }

  .title-text {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .title-badge {
    display: flex;
    align-items: center;
    background: rgba(0, 212, 170, 0.1);
    color: #00d4aa;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;

    text {
      margin-left: 4px;
    }
  }
}

/* 二维码容器 */
.qr-container {
  background: white;
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);

  .qr-image {
    width: 100%;
    max-width: 280px;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }
  }

  .qr-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    color: #666;
    font-size: 13px;

    text {
      margin-left: 6px;
    }
  }
}

/* ETC卡片 */
.etc-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  }

  .card-content {
    display: flex;
    align-items: center;
    padding: 20px;

    .card-image-container {
      width: 80px;
      height: 80px;
      margin-right: 16px;
      border-radius: 12px;
      overflow: hidden;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;

      .card-image {
        width: 100%;
        height: 100%;
      }
    }

    .card-info {
      flex: 1;

      .card-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 6px;
      }

      .card-desc {
        font-size: 13px;
        color: #999;
        margin-bottom: 12px;
      }

      .card-features {
        display: flex;
        gap: 16px;

        .feature-item {
          display: flex;
          align-items: center;

          text {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .card-action {
      padding: 8px 12px;
      border-radius: 8px;
      background: rgba(0, 212, 170, 0.1);
      transition: all 0.3s ease;

      text {
        font-size: 24rpx;
        color: #00d4aa;
        font-weight: 500;
      }
    }
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;

  .loading-text {
    color: #999;
    font-size: 28rpx;
  }
}

/* 空数据状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0;



  .empty-text {
    color: #666;
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 10rpx;
  }

  .empty-tip {
    color: #999;
    font-size: 24rpx;
  }
}
</style>

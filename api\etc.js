import { get } from "@/utils/requset.js";

/**
 * 获取启用状态的ETC二维码列表（小程序端专用）
 * 只返回状态为启用(1)的二维码记录，用于小程序端展示
 * @returns {Promise} 返回启用的ETC二维码列表
 */
const getActiveEtcQrcodeList = function () {
  console.log("API调用: getActiveEtcQrcodeList, 参数:", { status: 1 });
  return get("/etc/list", { status: 1 });
};

/**
 * 根据ID获取ETC二维码详情（小程序端专用）
 * @param {number} id - ETC二维码ID
 * @returns {Promise} 返回ETC二维码详情
 */
const getEtcQrcodeDetail = function (id) {
  return get("/etc/detail", { id });
};

export {
  getActiveEtcQrcodeList,
  getEtcQrcodeDetail,
};
